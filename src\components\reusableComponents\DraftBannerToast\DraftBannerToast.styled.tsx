import { Toast } from '@medly-components/core';
import styled from 'styled-components';
import { defaultTheme } from '@medly-components/theme';

export const DraftBannerToast = styled(Toast)`
    margin-bottom: 2rem;
    box-shadow: 1px 0 6px ${defaultTheme.colors.grey[400]};
    border-left: 0.4rem solid orange;
    width: 100%;
    position: relative;

    div:first-child {
        align-items: center;
    }

    div:nth-child(1) {
        background-color: rgba(255, 165, 0, 0.1);
    }

    div:nth-child(2) {
        padding: 0.8rem 1.6rem;
    }

    div:nth-child(3) {
        display: none;
    }

    svg:nth-child(1) {
        * {
            fill: orange;
        }
    }
`;
